/**
 * User management composable for LapXpert Admin Dashboard
 * Provides comprehensive user management functionality including role management and activity tracking
 */
import {
  UserRole,
  UserStatus,
  Gender,
  StaffWorkStatus
} from '~/types/shared'

import type {
  User,
  Customer,
  Staff,
  Address,
  UserAuditHistory,
  ChangeDetail,
  UserActivity,
  UserSearchFilters
} from '~/types/shared'

// Types are now imported from shared types file

export const useUser = () => {
  const { get, post, put, delete: del, execute } = useApi()

  /**
   * Get all customers
   */
  const getAllCustomers = async (search?: string): Promise<Customer[]> => {
    const params = search ? { search } : {}
    const { data, error } = await execute(
      () => get<Customer[]>('/user/customer', { params }),
      { errorContext: 'L<PERSON>y danh sách khách hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get all staff members
   */
  const getAllStaff = async (): Promise<Staff[]> => {
    const { data, error } = await execute(
      () => get<Staff[]>('/user/staff'),
      { errorContext: 'L<PERSON>y danh sách nhân viên' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get customer by ID
   */
  const getCustomerById = async (id: number): Promise<Customer | null> => {
    const { data, error } = await execute(
      () => get<Customer>(`/user/customer/${id}`),
      { errorContext: 'Lấy thông tin khách hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || null
  }

  /**
   * Get staff by ID
   */
  const getStaffById = async (id: number): Promise<Staff | null> => {
    const { data, error } = await execute(
      () => get<Staff>(`/user/staff/${id}`),
      { errorContext: 'Lấy thông tin nhân viên' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || null
  }

  /**
   * Create new customer
   */
  const createCustomer = async (customer: Customer): Promise<Customer> => {
    const { data, error } = await execute(
      () => post<Customer>('/user/customer', customer),
      { 
        successMessage: 'Thêm khách hàng thành công',
        showSuccessToast: true,
        errorContext: 'Thêm khách hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Create new staff
   */
  const createStaff = async (staff: Staff): Promise<Staff> => {
    const { data, error } = await execute(
      () => post<Staff>('/user/staff', staff),
      { 
        successMessage: 'Thêm nhân viên thành công',
        showSuccessToast: true,
        errorContext: 'Thêm nhân viên'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update customer
   */
  const updateCustomer = async (id: number, customer: Customer): Promise<Customer> => {
    const { data, error } = await execute(
      () => put<Customer>(`/user/customer/${id}`, customer),
      { 
        successMessage: 'Cập nhật khách hàng thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật khách hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update staff
   */
  const updateStaff = async (id: number, staff: Staff): Promise<Staff> => {
    const { data, error } = await execute(
      () => put<Staff>(`/user/staff/${id}`, staff),
      { 
        successMessage: 'Cập nhật nhân viên thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật nhân viên'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Delete customer (soft delete)
   */
  const deleteCustomer = async (id: number): Promise<void> => {
    const { error } = await execute(
      () => del(`/user/customer/${id}`),
      { 
        successMessage: 'Xóa khách hàng thành công',
        showSuccessToast: true,
        errorContext: 'Xóa khách hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Delete staff (soft delete)
   */
  const deleteStaff = async (id: number): Promise<void> => {
    const { error } = await execute(
      () => del(`/user/staff/${id}`),
      { 
        successMessage: 'Xóa nhân viên thành công',
        showSuccessToast: true,
        errorContext: 'Xóa nhân viên'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Restore customer
   */
  const restoreCustomer = async (id: number): Promise<void> => {
    const { error } = await execute(
      () => post(`/user/customer/restore/${id}`, null),
      { 
        successMessage: 'Khôi phục khách hàng thành công',
        showSuccessToast: true,
        errorContext: 'Khôi phục khách hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Restore staff
   */
  const restoreStaff = async (id: number): Promise<void> => {
    const { error } = await execute(
      () => post(`/user/staff/restore/${id}`, null),
      { 
        successMessage: 'Khôi phục nhân viên thành công',
        showSuccessToast: true,
        errorContext: 'Khôi phục nhân viên'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Validation functions
   */
  const isEmailAvailable = async (email: string): Promise<boolean> => {
    const { data, error } = await execute(
      () => get<boolean>(`/user/validate/email/${encodeURIComponent(email)}`),
      { errorContext: 'Kiểm tra email' }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data || false
  }

  const isPhoneAvailable = async (phone: string): Promise<boolean> => {
    const { data, error } = await execute(
      () => get<boolean>(`/user/validate/phone/${encodeURIComponent(phone)}`),
      { errorContext: 'Kiểm tra số điện thoại' }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data || false
  }

  const isCccdAvailable = async (cccd: string): Promise<boolean> => {
    const { data, error } = await execute(
      () => get<boolean>(`/user/validate/cccd/${encodeURIComponent(cccd)}`),
      { errorContext: 'Kiểm tra CCCD' }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data || false
  }

  /**
   * Get user audit history
   */
  const getCustomerAuditHistory = async (id: number): Promise<UserAuditHistory[]> => {
    const { data, error } = await execute(
      () => get<UserAuditHistory[]>(`/user/customer/${id}/audit-history`),
      { errorContext: 'Lấy lịch sử thay đổi khách hàng' }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  }

  const getStaffAuditHistory = async (id: number): Promise<UserAuditHistory[]> => {
    const { data, error } = await execute(
      () => get<UserAuditHistory[]>(`/user/staff/${id}/audit-history`),
      { errorContext: 'Lấy lịch sử thay đổi nhân viên' }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data || []
  }

  /**
   * Utility functions for display
   */
  const getUserRoleText = (role: UserRole): string => {
    const roleMap = {
      [UserRole.ADMIN]: 'Quản trị viên',
      [UserRole.STAFF]: 'Nhân viên',
      [UserRole.CUSTOMER]: 'Khách hàng'
    }
    return roleMap[role] || role
  }

  const getUserStatusText = (status: UserStatus): string => {
    const statusMap = {
      [UserStatus.HOAT_DONG]: 'Hoạt động',
      [UserStatus.KHONG_HOAT_DONG]: 'Không hoạt động',
      [UserStatus.BI_KHOA]: 'Bị khóa',
      [UserStatus.CHO_XAC_THUC]: 'Chờ xác thực'
    }
    return statusMap[status] || status
  }

  const getGenderText = (gender: Gender): string => {
    const genderMap = {
      [Gender.NAM]: 'Nam',
      [Gender.NU]: 'Nữ',
      [Gender.KHAC]: 'Khác'
    }
    return genderMap[gender] || gender
  }

  const getUserRoleSeverity = (role: UserRole): 'success' | 'info' | 'warn' | 'danger' => {
    const severityMap = {
      [UserRole.ADMIN]: 'danger' as const,
      [UserRole.STAFF]: 'info' as const,
      [UserRole.CUSTOMER]: 'success' as const
    }
    return severityMap[role] || 'info'
  }

  const getUserStatusSeverity = (status: UserStatus): 'success' | 'info' | 'warn' | 'danger' => {
    const severityMap = {
      [UserStatus.HOAT_DONG]: 'success' as const,
      [UserStatus.KHONG_HOAT_DONG]: 'warn' as const,
      [UserStatus.BI_KHOA]: 'danger' as const,
      [UserStatus.CHO_XAC_THUC]: 'info' as const
    }
    return severityMap[status] || 'info'
  }

  return {
    getAllCustomers,
    getAllStaff,
    getCustomerById,
    getStaffById,
    createCustomer,
    createStaff,
    updateCustomer,
    updateStaff,
    deleteCustomer,
    deleteStaff,
    restoreCustomer,
    restoreStaff,
    isEmailAvailable,
    isPhoneAvailable,
    isCccdAvailable,
    getCustomerAuditHistory,
    getStaffAuditHistory,
    getUserRoleText,
    getUserStatusText,
    getGenderText,
    getUserRoleSeverity,
    getUserStatusSeverity
  }
}
