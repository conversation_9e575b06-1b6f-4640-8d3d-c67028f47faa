/**
 * Order composable for LapXpert Admin Dashboard
 * Provides comprehensive order management functionality
 */
import {
  OrderStatus,
  PaymentStatus,
  OrderType
} from '~/types/shared'

import type {
  Order,
  TimelineEntry,
  OrderSearchFilters
} from '~/types/shared'

// Types are now imported from shared types file



export const useOrder = () => {
  const { get, post, put, execute } = useApi()

  /**
   * Get all orders with pagination and filtering
   */
  const getAllOrders = async (params: any = {}): Promise<{ content: Order[], totalElements: number }> => {
    const { data, error } = await execute(
      () => get<{ content: Order[], totalElements: number }>('/hoa-don', { params }),
      { errorContext: '<PERSON><PERSON>y danh sách đơn hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || { content: [], totalElements: 0 }
  }

  /**
   * Get order by ID
   */
  const getOrderById = async (id: number): Promise<Order | null> => {
    const { data, error } = await execute(
      () => get<Order>(`/hoa-don/${id}`),
      { errorContext: '<PERSON><PERSON><PERSON> thông tin đơn hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || null
  }

  /**
   * Create new order
   */
  const createOrder = async (order: Order): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>('/hoa-don', order),
      { 
        successMessage: 'Tạo đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Tạo đơn hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update order
   */
  const updateOrder = async (id: number, order: Order): Promise<Order> => {
    const { data, error } = await execute(
      () => put<Order>(`/hoa-don/${id}`, order),
      { 
        successMessage: 'Cập nhật đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật đơn hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Cancel order
   */
  const cancelOrder = async (id: number, reason?: string): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>(`/hoa-don/${id}/cancel`, null, { params: { reason } }),
      { 
        successMessage: 'Hủy đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Hủy đơn hàng'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Confirm payment
   */
  const confirmPayment = async (id: number, paymentMethod: string): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>(`/hoa-don/${id}/confirm-payment`, null, { params: { phuongThucThanhToan: paymentMethod } }),
      { 
        successMessage: 'Xác nhận thanh toán thành công',
        showSuccessToast: true,
        errorContext: 'Xác nhận thanh toán'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Get order audit history
   */
  const getOrderHistory = async (id: number): Promise<TimelineEntry[]> => {
    const { data, error } = await execute(
      () => get<TimelineEntry[]>(`/hoa-don/${id}/history`),
      { errorContext: 'Lấy lịch sử đơn hàng' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Search orders with filters
   */
  const searchOrders = async (filters: OrderSearchFilters, page: number = 0, size: number = 10): Promise<{ content: Order[], totalElements: number }> => {
    const params = {
      page,
      size,
      ...filters
    }

    return getAllOrders(params)
  }

  /**
   * Update order status
   */
  const updateOrderStatus = async (id: number, status: OrderStatus, reason?: string): Promise<Order> => {
    const { data, error } = await execute(
      () => post<Order>(`/hoa-don/${id}/update-status`, { trangThaiDonHang: status, lyDo: reason }),
      {
        successMessage: 'Cập nhật trạng thái đơn hàng thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật trạng thái đơn hàng'
      }
    )

    if (error) {
      throw new Error(error.message)
    }

    return data!
  }

  /**
   * Get order status display text
   */
  const getOrderStatusText = (status: OrderStatus): string => {
    const statusMap = {
      [OrderStatus.CHO_XAC_NHAN]: 'Chờ xác nhận',
      [OrderStatus.DA_XAC_NHAN]: 'Đã xác nhận',
      [OrderStatus.DANG_CHUAN_BI]: 'Đang chuẩn bị',
      [OrderStatus.DANG_GIAO]: 'Đang giao hàng',
      [OrderStatus.DA_GIAO]: 'Đã giao hàng',
      [OrderStatus.DA_HUY]: 'Đã hủy',
      [OrderStatus.TRA_HANG]: 'Trả hàng'
    }
    return statusMap[status] || status
  }

  /**
   * Get payment status display text
   */
  const getPaymentStatusText = (status: PaymentStatus): string => {
    const statusMap = {
      [PaymentStatus.CHUA_THANH_TOAN]: 'Chưa thanh toán',
      [PaymentStatus.DA_THANH_TOAN]: 'Đã thanh toán',
      [PaymentStatus.THANH_TOAN_MOT_PHAN]: 'Thanh toán một phần',
      [PaymentStatus.HOAN_TIEN]: 'Hoàn tiền'
    }
    return statusMap[status] || status
  }

  /**
   * Get order type display text
   */
  const getOrderTypeText = (type: OrderType): string => {
    const typeMap = {
      [OrderType.ONLINE]: 'Trực tuyến',
      [OrderType.TAI_QUAY]: 'Tại quầy'
    }
    return typeMap[type] || type
  }

  /**
   * Get order status severity for UI
   */
  const getOrderStatusSeverity = (status: OrderStatus): 'success' | 'info' | 'warn' | 'danger' => {
    const severityMap = {
      [OrderStatus.CHO_XAC_NHAN]: 'warn' as const,
      [OrderStatus.DA_XAC_NHAN]: 'info' as const,
      [OrderStatus.DANG_CHUAN_BI]: 'info' as const,
      [OrderStatus.DANG_GIAO]: 'info' as const,
      [OrderStatus.DA_GIAO]: 'success' as const,
      [OrderStatus.DA_HUY]: 'danger' as const,
      [OrderStatus.TRA_HANG]: 'warn' as const
    }
    return severityMap[status] || 'info'
  }

  /**
   * Get payment status severity for UI
   */
  const getPaymentStatusSeverity = (status: PaymentStatus): 'success' | 'info' | 'warn' | 'danger' => {
    const severityMap = {
      [PaymentStatus.CHUA_THANH_TOAN]: 'warn' as const,
      [PaymentStatus.DA_THANH_TOAN]: 'success' as const,
      [PaymentStatus.THANH_TOAN_MOT_PHAN]: 'info' as const,
      [PaymentStatus.HOAN_TIEN]: 'danger' as const
    }
    return severityMap[status] || 'info'
  }

  return {
    getAllOrders,
    getOrderById,
    createOrder,
    updateOrder,
    cancelOrder,
    confirmPayment,
    getOrderHistory,
    searchOrders,
    updateOrderStatus,
    getOrderStatusText,
    getPaymentStatusText,
    getOrderTypeText,
    getOrderStatusSeverity,
    getPaymentStatusSeverity
  }
}
