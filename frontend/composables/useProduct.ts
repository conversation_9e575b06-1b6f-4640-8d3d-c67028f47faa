/**
 * Product composable for LapXpert Admin Dashboard
 * Provides product management functionality including CRUD operations
 */
import type {
  Product,
  ProductVariant,
  Brand,
  Category,
  Cpu,
  Ram,
  Gpu,
  Color,
  Storage,
  Screen,
  ProductSearchFilters
} from '~/types/shared'

// Types are now imported from shared types file

export const useProduct = () => {
  const { get, post, put, delete: del, execute } = useApi()
  const toast = useToast()

  /**
   * Get all products
   */
  const getAllProducts = async (): Promise<Product[]> => {
    const { data, error } = await execute(
      () => get<Product[]>('/products'),
      { errorContext: '<PERSON><PERSON><PERSON> danh sách sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get active products only
   */
  const getActiveProducts = async (): Promise<Product[]> => {
    const { data, error } = await execute(
      () => get<Product[]>('/products/list'),
      { errorContext: '<PERSON><PERSON><PERSON> danh sách sản phẩm hoạt động' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get product by ID
   */
  const getProductById = async (id: number): Promise<Product | null> => {
    const { data, error } = await execute(
      () => get<Product>(`/products/${id}`),
      { errorContext: 'Lấy thông tin sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || null
  }

  /**
   * Create new product
   */
  const createProduct = async (product: Product): Promise<Product> => {
    const { data, error } = await execute(
      () => post<Product>('/products/add', product),
      { 
        successMessage: 'Thêm sản phẩm thành công',
        showSuccessToast: true,
        errorContext: 'Thêm sản phẩm'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update product
   */
  const updateProduct = async (id: number, product: Product): Promise<Product> => {
    const { data, error } = await execute(
      () => put<Product>(`/products/update/${id}`, product),
      { 
        successMessage: 'Cập nhật sản phẩm thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật sản phẩm'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Update product with variants
   */
  const updateProductWithVariants = async (id: number, product: Product): Promise<Product> => {
    const { data, error } = await execute(
      () => put<Product>(`/products/updateWithVariants/${id}`, product),
      { 
        successMessage: 'Cập nhật sản phẩm và biến thể thành công',
        showSuccessToast: true,
        errorContext: 'Cập nhật sản phẩm với biến thể'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data!
  }

  /**
   * Soft delete product
   */
  const deleteProduct = async (id: number): Promise<void> => {
    const { error } = await execute(
      () => del(`/products/delete/${id}`),
      { 
        successMessage: 'Xóa sản phẩm thành công',
        showSuccessToast: true,
        errorContext: 'Xóa sản phẩm'
      }
    )
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Search products with filters
   */
  const searchProducts = async (filters: ProductSearchFilters): Promise<Product[]> => {
    const { data, error } = await execute(
      () => post<Product[]>('/products/search', filters),
      { errorContext: 'Tìm kiếm sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  /**
   * Get product audit history
   */
  const getProductAuditHistory = async (id: number) => {
    const { data, error } = await execute(
      () => get(`/products/${id}/audit-history`),
      { errorContext: 'Lấy lịch sử thay đổi sản phẩm' }
    )
    
    if (error) {
      throw new Error(error.message)
    }
    
    return data || []
  }

  return {
    getAllProducts,
    getActiveProducts,
    getProductById,
    createProduct,
    updateProduct,
    updateProductWithVariants,
    deleteProduct,
    searchProducts,
    getProductAuditHistory
  }
}
