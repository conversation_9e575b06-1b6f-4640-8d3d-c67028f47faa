/**
 * Shared type definitions for LapXpert Admin Dashboard
 * Centralized location for all common types to avoid duplication
 */

// ============================================================================
// USER TYPES
// ============================================================================

export enum UserRole {
  ADMIN = 'ADMIN',
  STAFF = 'STAFF', 
  CUSTOMER = 'CUSTOMER'
}

export enum UserStatus {
  HOAT_DONG = 'HOAT_DONG',
  KHONG_HOAT_DONG = 'KHONG_HOAT_DONG',
  BI_KHOA = 'BI_KHOA',
  CHO_XAC_THUC = 'CHO_XAC_THUC'
}

export enum Gender {
  NAM = 'NAM',
  NU = 'NU',
  KHAC = 'KHAC'
}

export enum StaffWorkStatus {
  DANG_LAM_VIEC = 'DANG_LAM_VIEC',
  NGHI_PHEP = 'NGHI_PHEP',
  NGHI_VIEC = 'NGHI_VIEC',
  THOI_VIEC = 'THOI_VIEC'
}

export interface User {
  id: number
  email: string
  hoTen: string
  vaiTro: 'ADMIN' | 'STAFF' | 'CUSTOMER'
  trangThai: boolean
  avatar?: string
}

export interface Customer {
  id?: number
  maNguoiDung?: string
  avatar?: string
  hoTen: string
  gioiTinh?: Gender
  ngaySinh?: string
  email: string
  soDienThoai: string
  cccd?: string
  matKhau?: string
  vaiTro: 'CUSTOMER'
  trangThai: UserStatus
  ngayTao?: string
  ngayCapNhat?: string
  nguoiTao?: string
  nguoiCapNhat?: string
  diaChis?: Address[]
  hoaDons?: any[]
  gioHang?: any
  danhSachYeuThichs?: any[]
  danhGias?: any[]
  diemTichLuy?: number
  hangThanhVien?: string
  ngayDangKy?: string
}

export interface Staff {
  id?: number
  maNguoiDung?: string
  avatar?: string
  hoTen: string
  gioiTinh?: Gender
  ngaySinh?: string
  email: string
  soDienThoai: string
  cccd?: string
  matKhau?: string
  vaiTro: 'STAFF' | 'ADMIN'
  trangThai: UserStatus
  ngayTao?: string
  ngayCapNhat?: string
  nguoiTao?: string
  nguoiCapNhat?: string
  diaChis?: Address[]
  hoaDons?: any[]
  gioHang?: any
  danhSachYeuThichs?: any[]
  danhGias?: any[]
  chucVu?: string
  phongBan?: string
  maNhanVien?: string
  ngayVaoLam?: string
  luongCoBan?: number
  trangThaiLamViec?: StaffWorkStatus
}

export interface Address {
  id?: number
  hoTen?: string
  soDienThoai?: string
  diaChiChiTiet?: string
  phuongXa?: string
  quanHuyen?: string
  tinhThanhPho?: string
  loaiDiaChi?: string
  macDinh?: boolean
  ghiChu?: string
  ngayTao?: string
  ngayCapNhat?: string
}

export interface ChangeDetail {
  field: string
  oldValue: any
  newValue: any
  changeType: 'CREATE' | 'UPDATE' | 'DELETE'
}

export interface UserAuditHistory {
  id?: number
  entityId: number
  entityType: string
  action: string
  changes: ChangeDetail[]
  performedBy: string
  performedAt: string
  ipAddress?: string
  userAgent?: string
}

export interface UserActivity {
  id?: number
  userId: number
  action: string
  description: string
  timestamp: string
  ipAddress?: string
  userAgent?: string
}

export interface UserSearchFilters {
  hoTen?: string
  email?: string
  soDienThoai?: string
  vaiTro?: UserRole
  trangThai?: UserStatus
  gioiTinh?: Gender
  tuNgay?: string
  denNgay?: string
}

// ============================================================================
// PRODUCT TYPES  
// ============================================================================

export interface Product {
  id?: number
  maSanPham?: string
  tenSanPham: string
  moTa?: string
  hinhAnh?: string[]
  ngayRaMat?: string
  trangThai?: boolean
  thuongHieu?: Brand
  danhMucs?: Category[]
  sanPhamChiTiets?: ProductVariant[]
  ngayTao?: string
  ngayCapNhat?: string
  nguoiTao?: string
  nguoiCapNhat?: string
}

export interface ProductVariant {
  id?: number
  sku?: string
  giaBan: number
  giaKhuyenMai?: number
  soLuongTon?: number
  trangThai?: boolean
  hinhAnh?: string[]
  cpu?: Cpu
  ram?: Ram
  gpu?: Gpu
  mauSac?: Color
  oCung?: Storage
  manHinh?: Screen
  ngayTao?: string
  ngayCapNhat?: string
}

export interface Brand {
  id?: number
  maThuongHieu?: string
  moTaThuongHieu: string
  trangThai?: boolean
}

export interface Category {
  id?: number
  maDanhMuc?: string
  tenDanhMuc: string
  moTa?: string
  trangThai?: boolean
}

export interface Cpu {
  id?: number
  maCpu?: string
  tenCpu: string
  trangThai?: boolean
}

export interface Ram {
  id?: number
  maRam?: string
  dungLuongRam: string
  trangThai?: boolean
}

export interface Gpu {
  id?: number
  maGpu?: string
  tenGpu: string
  trangThai?: boolean
}

export interface Color {
  id?: number
  maMauSac?: string
  tenMauSac: string
  trangThai?: boolean
}

export interface Storage {
  id?: number
  maOCung?: string
  dungLuongOCung: string
  loaiOCung?: string
  trangThai?: boolean
}

export interface Screen {
  id?: number
  maManHinh?: string
  kichThuocManHinh: string
  doPhanGiai?: string
  trangThai?: boolean
}

export interface ProductSearchFilters {
  tenSanPham?: string
  thuongHieuId?: number
  danhMucIds?: number[]
  trangThai?: boolean
  minPrice?: number
  maxPrice?: number
}

// ============================================================================
// ORDER TYPES
// ============================================================================

export enum OrderStatus {
  CHO_XAC_NHAN = 'CHO_XAC_NHAN',
  DA_XAC_NHAN = 'DA_XAC_NHAN', 
  DANG_CHUAN_BI = 'DANG_CHUAN_BI',
  DANG_GIAO = 'DANG_GIAO',
  DA_GIAO = 'DA_GIAO',
  DA_HUY = 'DA_HUY',
  TRA_HANG = 'TRA_HANG'
}

export enum PaymentStatus {
  CHUA_THANH_TOAN = 'CHUA_THANH_TOAN',
  DA_THANH_TOAN = 'DA_THANH_TOAN',
  THANH_TOAN_MOT_PHAN = 'THANH_TOAN_MOT_PHAN',
  HOAN_TIEN = 'HOAN_TIEN'
}

export enum OrderType {
  ONLINE = 'ONLINE',
  TAI_QUAY = 'TAI_QUAY'
}

export interface Order {
  id?: number
  maHoaDon?: string
  khachHangId?: number
  nhanVienId?: number
  khachHang?: Customer
  nhanVien?: Staff
  diaChiGiaoHangId?: number
  diaChiGiaoHang?: Address
  nguoiNhanTen?: string
  nguoiNhanSdt?: string
  ngayTao?: string
  ngayCapNhat?: string
  tongTienHang?: number
  giaTriGiamGiaVoucher?: number
  phiVanChuyen?: number
  tongThanhToan?: number
  trangThaiDonHang?: OrderStatus
  trangThaiThanhToan?: PaymentStatus
  loaiHoaDon?: OrderType
  maVanDon?: string
  ngayDuKienGiaoHang?: string
  ghiChu?: string
  chiTiet?: OrderItem[]
  hoaDonPhieuGiamGias?: OrderVoucher[]
  voucherCodes?: string[]
  nguoiTao?: string
  nguoiCapNhat?: string
}

export interface OrderItem {
  id?: number
  hoaDonId?: number
  sanPhamChiTietId?: number
  sanPhamChiTiet?: ProductVariant
  soLuong: number
  giaGoc?: number
  giaBan: number
  thanhTien?: number
  tenSanPhamSnapshot?: string
  skuSnapshot?: string
  hinhAnhSnapshot?: string
  ngayTao?: string
  ngayCapNhat?: string
  ghiChu?: string
}

export interface OrderVoucher {
  id?: number
  phieuGiamGia?: Voucher
  soTienGiam?: number
}

export interface OrderAuditHistory {
  id?: number
  hoaDonId: number
  hanhDong: string
  moTa: string
  nguoiThucHien: string
  thoiGian: string
  giaTri?: any
}

export interface TimelineEntry {
  status: OrderStatus
  timestamp: string
  description: string
  user?: string
  note?: string
}

export interface OrderSearchFilters {
  maHoaDon?: string
  khachHang?: string
  trangThaiDonHang?: OrderStatus[]
  trangThaiThanhToan?: PaymentStatus[]
  loaiHoaDon?: OrderType
  tuNgay?: string
  denNgay?: string
  minAmount?: number
  maxAmount?: number
}

// ============================================================================
// VOUCHER TYPES
// ============================================================================

export interface Voucher {
  id?: number
  maPhieuGiamGia: string
  loaiGiamGia: 'PHAN_TRAM' | 'SO_TIEN_CO_DINH'
  trangThai: 'CHUA_DIEN_RA' | 'DA_DIEN_RA' | 'DA_KET_THUC' | 'DA_HUY'
  giaTriGiam: number
  giaTriDonHangToiThieu?: number
  ngayBatDau: string
  ngayKetThuc: string
  moTa?: string
  soLuongBanDau: number
  soLuongDaDung: number
  ngayTao?: string
  ngayCapNhat?: string
  danhSachNguoiDung?: number[]
  lyDoThayDoi?: string
}

export interface VoucherValidationRequest {
  voucherCode: string
  customerId?: number
  orderTotal: number
}

export interface VoucherValidationResponse {
  valid: boolean
  errorMessage?: string
  voucher?: Voucher
  discountAmount?: number
}

export interface VoucherFilters {
  status: string
  type: string
  dateRange: [Date, Date] | null
  search: string
  customerType: 'all' | 'public' | 'private'
}

export interface VoucherStats {
  totalVouchers: number
  activeVouchers: number
  expiredVouchers: number
  totalUsage: number
  totalDiscount: number
}
