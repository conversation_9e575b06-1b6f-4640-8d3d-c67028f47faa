/**
 * Guest middleware for LapXpert Admin Dashboard
 * Redirects authenticated users away from guest-only pages (like login)
 */

export default defineNuxtRouteMiddleware((to, from) => {
  // Skip check on server-side during SSR
  if (import.meta.server) {
    return
  }

  const { isAuthenticated } = useAuth()

  // If user is already authenticated, redirect to dashboard
  if (isAuthenticated.value) {
    return navigateTo('/')
  }
})
