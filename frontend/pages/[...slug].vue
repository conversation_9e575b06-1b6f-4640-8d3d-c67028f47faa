<template>
  <div class="flex items-center justify-center min-h-screen">
    <div class="text-center">
      <h1 class="text-4xl font-bold text-gray-800 mb-4">404</h1>
      <p class="text-gray-600 mb-6">Trang không tồn tại</p>
      <NuxtLink 
        to="/" 
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Về trang chủ
      </NuxtLink>
    </div>
  </div>
</template>

<script setup>
// This catch-all route handles any unmatched paths
// including Chrome DevTools paths like /.well-known/appspecific/com.chrome.devtools.json

// Set appropriate status code for 404
setResponseStatus(404)

// Set page meta
useHead({
  title: '404 - Trang không tồn tại'
})
</script>
